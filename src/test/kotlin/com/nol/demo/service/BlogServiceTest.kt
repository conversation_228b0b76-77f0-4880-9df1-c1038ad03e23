package com.nol.demo.service

import com.nol.demo.dto.BlogCreateRequest
import com.nol.demo.dto.BlogUpdateRequest
import com.nol.demo.entity.Blog
import com.nol.demo.repository.BlogRepository
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class BlogServiceTest {

    @Mock
    private lateinit var blogRepository: BlogRepository

    @InjectMocks
    private lateinit var blogService: BlogService

    private lateinit var testBlog: Blog

    @BeforeEach
    fun setUp() {
        testBlog = Blog(
            id = 1L,
            title = "테스트 블로그",
            content = "테스트 내용입니다.",
            author = "테스트 작성자",
            createdAt = LocalDateTime.now().minusDays(1),
            updatedAt = LocalDateTime.now().minusDays(1)
        )
    }

    @Test
    fun `모든 블로그 글 조회 테스트`() {
        // given
        val blogs = listOf(testBlog)
        whenever(blogRepository.findAll()).thenReturn(blogs)

        // when
        val result = blogService.getAllBlogs()

        // then
        assertEquals(1, result.size)
        assertEquals(testBlog.title, result[0].title)
        verify(blogRepository).findAll()
    }

    @Test
    fun `ID로 블로그 글 조회 성공 테스트`() {
        // given
        whenever(blogRepository.findById(1L)).thenReturn(Optional.of(testBlog))

        // when
        val result = blogService.getBlogById(1L)

        // then
        assertEquals(testBlog.title, result.title)
        assertEquals(testBlog.content, result.content)
        assertEquals(testBlog.author, result.author)
        verify(blogRepository).findById(1L)
    }

    @Test
    fun `ID로 블로그 글 조회 실패 테스트`() {
        // given
        whenever(blogRepository.findById(999L)).thenReturn(Optional.empty())

        // when & then
        assertThrows<IllegalArgumentException> {
            blogService.getBlogById(999L)
        }
        verify(blogRepository).findById(999L)
    }

    @Test
    fun `블로그 글 생성 테스트`() {
        // given
        val createRequest = BlogCreateRequest(
            title = "새 블로그 글",
            content = "새 내용",
            author = "새 작성자"
        )
        val savedBlog = testBlog.copy(
            title = createRequest.title,
            content = createRequest.content,
            author = createRequest.author
        )
        whenever(blogRepository.save(any<Blog>())).thenReturn(savedBlog)

        // when
        val result = blogService.createBlog(createRequest)

        // then
        assertEquals(createRequest.title, result.title)
        assertEquals(createRequest.content, result.content)
        assertEquals(createRequest.author, result.author)
        verify(blogRepository).save(any<Blog>())
    }

    @Test
    fun `블로그 글 수정 성공 테스트`() {
        // given
        val updateRequest = BlogUpdateRequest(
            title = "수정된 제목",
            content = "수정된 내용",
            author = "수정된 작성자"
        )
        val updatedBlog = testBlog.copy(
            title = updateRequest.title,
            content = updateRequest.content,
            author = updateRequest.author,
            updatedAt = LocalDateTime.now()
        )
        
        whenever(blogRepository.findById(1L)).thenReturn(Optional.of(testBlog))
        whenever(blogRepository.save(any<Blog>())).thenReturn(updatedBlog)

        // when
        val result = blogService.updateBlog(1L, updateRequest)

        // then
        assertEquals(updateRequest.title, result.title)
        assertEquals(updateRequest.content, result.content)
        assertEquals(updateRequest.author, result.author)
        verify(blogRepository).findById(1L)
        verify(blogRepository).save(any<Blog>())
    }

    @Test
    fun `블로그 글 수정 실패 테스트`() {
        // given
        val updateRequest = BlogUpdateRequest(
            title = "수정된 제목",
            content = "수정된 내용",
            author = "수정된 작성자"
        )
        whenever(blogRepository.findById(999L)).thenReturn(Optional.empty())

        // when & then
        assertThrows<IllegalArgumentException> {
            blogService.updateBlog(999L, updateRequest)
        }
        verify(blogRepository).findById(999L)
        verify(blogRepository, never()).save(any<Blog>())
    }

    @Test
    fun `블로그 글 삭제 성공 테스트`() {
        // given
        whenever(blogRepository.existsById(1L)).thenReturn(true)

        // when
        blogService.deleteBlog(1L)

        // then
        verify(blogRepository).existsById(1L)
        verify(blogRepository).deleteById(1L)
    }

    @Test
    fun `블로그 글 삭제 실패 테스트`() {
        // given
        whenever(blogRepository.existsById(999L)).thenReturn(false)

        // when & then
        assertThrows<IllegalArgumentException> {
            blogService.deleteBlog(999L)
        }
        verify(blogRepository).existsById(999L)
        verify(blogRepository, never()).deleteById(any())
    }

    @Test
    fun `작성자별 블로그 글 조회 테스트`() {
        // given
        val blogs = listOf(testBlog)
        whenever(blogRepository.findByAuthor("테스트 작성자")).thenReturn(blogs)

        // when
        val result = blogService.getBlogsByAuthor("테스트 작성자")

        // then
        assertEquals(1, result.size)
        assertEquals(testBlog.author, result[0].author)
        verify(blogRepository).findByAuthor("테스트 작성자")
    }

    @Test
    fun `제목으로 블로그 글 검색 테스트`() {
        // given
        val blogs = listOf(testBlog)
        whenever(blogRepository.findByTitleContainingIgnoreCase("테스트")).thenReturn(blogs)

        // when
        val result = blogService.searchBlogsByTitle("테스트")

        // then
        assertEquals(1, result.size)
        assertTrue(result[0].title.contains("테스트"))
        verify(blogRepository).findByTitleContainingIgnoreCase("테스트")
    }
}
