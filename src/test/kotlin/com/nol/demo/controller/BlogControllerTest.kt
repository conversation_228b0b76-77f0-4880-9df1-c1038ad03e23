package com.nol.demo.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.nol.demo.dto.BlogCreateRequest
import com.nol.demo.dto.BlogResponse
import com.nol.demo.dto.BlogUpdateRequest
import com.nol.demo.service.BlogService
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.mockito.kotlin.*
import java.time.LocalDateTime

@WebMvcTest(BlogController::class)
class BlogControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockBean
    private lateinit var blogService: BlogService

    private val testBlogResponse = BlogResponse(
        id = 1L,
        title = "테스트 블로그",
        content = "테스트 내용입니다.",
        author = "테스트 작성자",
        createdAt = LocalDateTime.now().minusDays(1),
        updatedAt = LocalDateTime.now().minusDays(1)
    )

    @Test
    fun `모든 블로그 글 조회 API 테스트`() {
        // given
        val blogs = listOf(testBlogResponse)
        whenever(blogService.getAllBlogs()).thenReturn(blogs)

        // when & then
        mockMvc.perform(get("/api/blogs"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$[0].id").value(1))
            .andExpect(jsonPath("$[0].title").value("테스트 블로그"))
            .andExpect(jsonPath("$[0].author").value("테스트 작성자"))

        verify(blogService).getAllBlogs()
    }

    @Test
    fun `ID로 블로그 글 조회 성공 API 테스트`() {
        // given
        whenever(blogService.getBlogById(1L)).thenReturn(testBlogResponse)

        // when & then
        mockMvc.perform(get("/api/blogs/1"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id").value(1))
            .andExpect(jsonPath("$.title").value("테스트 블로그"))
            .andExpect(jsonPath("$.author").value("테스트 작성자"))

        verify(blogService).getBlogById(1L)
    }

    @Test
    fun `ID로 블로그 글 조회 실패 API 테스트`() {
        // given
        whenever(blogService.getBlogById(999L)).thenThrow(IllegalArgumentException("Blog not found"))

        // when & then
        mockMvc.perform(get("/api/blogs/999"))
            .andExpect(status().isNotFound)

        verify(blogService).getBlogById(999L)
    }

    @Test
    fun `블로그 글 생성 API 테스트`() {
        // given
        val createRequest = BlogCreateRequest(
            title = "새 블로그 글",
            content = "새 내용",
            author = "새 작성자"
        )
        val createdBlog = testBlogResponse.copy(
            title = createRequest.title,
            content = createRequest.content,
            author = createRequest.author
        )
        whenever(blogService.createBlog(any())).thenReturn(createdBlog)

        // when & then
        mockMvc.perform(
            post("/api/blogs")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isCreated)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.title").value("새 블로그 글"))
            .andExpect(jsonPath("$.content").value("새 내용"))
            .andExpect(jsonPath("$.author").value("새 작성자"))

        verify(blogService).createBlog(any())
    }

    @Test
    fun `블로그 글 수정 성공 API 테스트`() {
        // given
        val updateRequest = BlogUpdateRequest(
            title = "수정된 제목",
            content = "수정된 내용",
            author = "수정된 작성자"
        )
        val updatedBlog = testBlogResponse.copy(
            title = updateRequest.title,
            content = updateRequest.content,
            author = updateRequest.author
        )
        whenever(blogService.updateBlog(eq(1L), any())).thenReturn(updatedBlog)

        // when & then
        mockMvc.perform(
            put("/api/blogs/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.title").value("수정된 제목"))
            .andExpect(jsonPath("$.content").value("수정된 내용"))
            .andExpect(jsonPath("$.author").value("수정된 작성자"))

        verify(blogService).updateBlog(eq(1L), any())
    }

    @Test
    fun `블로그 글 수정 실패 API 테스트`() {
        // given
        val updateRequest = BlogUpdateRequest(
            title = "수정된 제목",
            content = "수정된 내용",
            author = "수정된 작성자"
        )
        whenever(blogService.updateBlog(eq(999L), any())).thenThrow(IllegalArgumentException("Blog not found"))

        // when & then
        mockMvc.perform(
            put("/api/blogs/999")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isNotFound)

        verify(blogService).updateBlog(eq(999L), any())
    }

    @Test
    fun `블로그 글 삭제 성공 API 테스트`() {
        // given
        doNothing().whenever(blogService).deleteBlog(1L)

        // when & then
        mockMvc.perform(delete("/api/blogs/1"))
            .andExpect(status().isNoContent)

        verify(blogService).deleteBlog(1L)
    }

    @Test
    fun `블로그 글 삭제 실패 API 테스트`() {
        // given
        doThrow(IllegalArgumentException("Blog not found")).whenever(blogService).deleteBlog(999L)

        // when & then
        mockMvc.perform(delete("/api/blogs/999"))
            .andExpect(status().isNotFound)

        verify(blogService).deleteBlog(999L)
    }

    @Test
    fun `작성자별 블로그 글 조회 API 테스트`() {
        // given
        val blogs = listOf(testBlogResponse)
        whenever(blogService.getBlogsByAuthor("테스트 작성자")).thenReturn(blogs)

        // when & then
        mockMvc.perform(get("/api/blogs/author/테스트 작성자"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$[0].author").value("테스트 작성자"))

        verify(blogService).getBlogsByAuthor("테스트 작성자")
    }

    @Test
    fun `제목으로 블로그 글 검색 API 테스트`() {
        // given
        val blogs = listOf(testBlogResponse)
        whenever(blogService.searchBlogsByTitle("테스트")).thenReturn(blogs)

        // when & then
        mockMvc.perform(get("/api/blogs/search").param("title", "테스트"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$[0].title").value("테스트 블로그"))

        verify(blogService).searchBlogsByTitle("테스트")
    }
}
