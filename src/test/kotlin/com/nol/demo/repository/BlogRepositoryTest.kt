package com.nol.demo.repository

import com.nol.demo.entity.Blog
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@DataJpaTest
class BlogRepositoryTest {

    @Autowired
    private lateinit var entityManager: TestEntityManager

    @Autowired
    private lateinit var blogRepository: BlogRepository

    private lateinit var testBlog1: Blog
    private lateinit var testBlog2: Blog
    private lateinit var testBlog3: Blog

    @BeforeEach
    fun setUp() {
        testBlog1 = Blog(
            title = "Spring Boot 테스트",
            content = "Spring Boot 테스트 방법에 대해 알아봅시다.",
            author = "김테스트",
            createdAt = LocalDateTime.now().minusDays(1),
            updatedAt = LocalDateTime.now().minusDays(1)
        )

        testBlog2 = Blog(
            title = "JPA 활용법",
            content = "JPA를 효과적으로 사용하는 방법을 소개합니다.",
            author = "박JPA",
            createdAt = LocalDateTime.now().minusDays(2),
            updatedAt = LocalDateTime.now().minusDays(2)
        )

        testBlog3 = Blog(
            title = "Kotlin Spring Boot",
            content = "Kotlin과 Spring Boot를 함께 사용하는 방법입니다.",
            author = "김테스트",
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        entityManager.persistAndFlush(testBlog1)
        entityManager.persistAndFlush(testBlog2)
        entityManager.persistAndFlush(testBlog3)
    }

    @Test
    fun `작성자로 블로그 글 찾기 테스트`() {
        // when
        val blogs = blogRepository.findByAuthor("김테스트")

        // then
        assertEquals(2, blogs.size)
        assertTrue(blogs.all { it.author == "김테스트" })
    }

    @Test
    fun `제목으로 블로그 글 검색 테스트`() {
        // when
        val blogs = blogRepository.findByTitleContainingIgnoreCase("spring")

        // then
        assertEquals(2, blogs.size)
        assertTrue(blogs.any { it.title.contains("Spring Boot") })
        assertTrue(blogs.any { it.title.contains("Kotlin Spring Boot") })
    }

    @Test
    fun `제목 검색 대소문자 구분 없음 테스트`() {
        // when
        val blogs = blogRepository.findByTitleContainingIgnoreCase("SPRING")

        // then
        assertEquals(2, blogs.size)
    }

    @Test
    fun `존재하지 않는 작성자로 검색 테스트`() {
        // when
        val blogs = blogRepository.findByAuthor("존재하지않는작성자")

        // then
        assertTrue(blogs.isEmpty())
    }

    @Test
    fun `존재하지 않는 제목으로 검색 테스트`() {
        // when
        val blogs = blogRepository.findByTitleContainingIgnoreCase("존재하지않는제목")

        // then
        assertTrue(blogs.isEmpty())
    }

    @Test
    fun `모든 블로그 글 조회 테스트`() {
        // when
        val blogs = blogRepository.findAll()

        // then
        assertEquals(3, blogs.size)
    }

    @Test
    fun `블로그 글 저장 테스트`() {
        // given
        val newBlog = Blog(
            title = "새로운 블로그 글",
            content = "새로운 내용입니다.",
            author = "새작성자"
        )

        // when
        val savedBlog = blogRepository.save(newBlog)

        // then
        assertEquals("새로운 블로그 글", savedBlog.title)
        assertEquals("새로운 내용입니다.", savedBlog.content)
        assertEquals("새작성자", savedBlog.author)
        assertTrue(savedBlog.id != null)
    }
}
