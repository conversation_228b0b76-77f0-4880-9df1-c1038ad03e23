package com.nol.demo.controller

import com.nol.demo.dto.BlogCreateRequest
import com.nol.demo.dto.BlogResponse
import com.nol.demo.dto.BlogUpdateRequest
import com.nol.demo.service.BlogService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/blogs")
@Tag(name = "Blog API", description = "블로그 글 관리 API")
class BlogController(
    private val blogService: BlogService
) {
    
    @GetMapping
    @Operation(summary = "모든 블로그 글 조회", description = "등록된 모든 블로그 글을 조회합니다.")
    @ApiResponse(responseCode = "200", description = "조회 성공")
    fun getAllBlogs(): ResponseEntity<List<BlogResponse>> {
        val blogs = blogService.getAllBlogs()
        return ResponseEntity.ok(blogs)
    }

    @GetMapping("/{id}")
    @Operation(summary = "블로그 글 상세 조회", description = "ID로 특정 블로그 글을 조회합니다.")
    @ApiResponses(
        ApiResponse(responseCode = "200", description = "조회 성공"),
        ApiResponse(responseCode = "404", description = "블로그 글을 찾을 수 없음")
    )
    fun getBlogById(
        @Parameter(description = "블로그 글 ID", required = true)
        @PathVariable id: Long
    ): ResponseEntity<BlogResponse> {
        return try {
            val blog = blogService.getBlogById(id)
            ResponseEntity.ok(blog)
        } catch (e: IllegalArgumentException) {
            ResponseEntity.notFound().build()
        }
    }

    @PostMapping
    @Operation(summary = "블로그 글 생성", description = "새로운 블로그 글을 생성합니다.")
    @ApiResponses(
        ApiResponse(responseCode = "201", description = "생성 성공"),
        ApiResponse(responseCode = "400", description = "잘못된 요청")
    )
    fun createBlog(@RequestBody request: BlogCreateRequest): ResponseEntity<BlogResponse> {
        val createdBlog = blogService.createBlog(request)
        return ResponseEntity.status(HttpStatus.CREATED).body(createdBlog)
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "블로그 글 수정", description = "기존 블로그 글을 수정합니다.")
    @ApiResponses(
        ApiResponse(responseCode = "200", description = "수정 성공"),
        ApiResponse(responseCode = "404", description = "블로그 글을 찾을 수 없음")
    )
    fun updateBlog(
        @Parameter(description = "블로그 글 ID", required = true)
        @PathVariable id: Long,
        @RequestBody request: BlogUpdateRequest
    ): ResponseEntity<BlogResponse> {
        return try {
            val updatedBlog = blogService.updateBlog(id, request)
            ResponseEntity.ok(updatedBlog)
        } catch (e: IllegalArgumentException) {
            ResponseEntity.notFound().build()
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "블로그 글 삭제", description = "블로그 글을 삭제합니다.")
    @ApiResponses(
        ApiResponse(responseCode = "204", description = "삭제 성공"),
        ApiResponse(responseCode = "404", description = "블로그 글을 찾을 수 없음")
    )
    fun deleteBlog(
        @Parameter(description = "블로그 글 ID", required = true)
        @PathVariable id: Long
    ): ResponseEntity<Void> {
        return try {
            blogService.deleteBlog(id)
            ResponseEntity.noContent().build()
        } catch (e: IllegalArgumentException) {
            ResponseEntity.notFound().build()
        }
    }

    @GetMapping("/author/{author}")
    @Operation(summary = "작성자별 블로그 글 조회", description = "특정 작성자의 모든 블로그 글을 조회합니다.")
    @ApiResponse(responseCode = "200", description = "조회 성공")
    fun getBlogsByAuthor(
        @Parameter(description = "작성자 이름", required = true)
        @PathVariable author: String
    ): ResponseEntity<List<BlogResponse>> {
        val blogs = blogService.getBlogsByAuthor(author)
        return ResponseEntity.ok(blogs)
    }

    @GetMapping("/search")
    @Operation(summary = "제목으로 블로그 글 검색", description = "제목에 특정 키워드가 포함된 블로그 글을 검색합니다.")
    @ApiResponse(responseCode = "200", description = "검색 성공")
    fun searchBlogsByTitle(
        @Parameter(description = "검색할 제목 키워드", required = true)
        @RequestParam title: String
    ): ResponseEntity<List<BlogResponse>> {
        val blogs = blogService.searchBlogsByTitle(title)
        return ResponseEntity.ok(blogs)
    }
}
