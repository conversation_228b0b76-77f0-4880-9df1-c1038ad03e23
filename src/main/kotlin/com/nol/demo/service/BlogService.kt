package com.nol.demo.service

import com.nol.demo.dto.BlogCreateRequest
import com.nol.demo.dto.BlogResponse
import com.nol.demo.dto.BlogUpdateRequest
import com.nol.demo.entity.Blog
import com.nol.demo.repository.BlogRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
@Transactional
class BlogService(
    private val blogRepository: BlogRepository
) {
    
    fun getAllBlogs(): List<BlogResponse> {
        return blogRepository.findAll().map { BlogResponse.from(it) }
    }
    
    fun getBlogById(id: Long): BlogResponse {
        val blog = blogRepository.findById(id)
            .orElseThrow { IllegalArgumentException("Blog not found with id: $id") }
        return BlogResponse.from(blog)
    }
    
    fun createBlog(request: BlogCreateRequest): BlogResponse {
        val blog = Blog(
            title = request.title,
            content = request.content,
            author = request.author
        )
        val savedBlog = blogRepository.save(blog)
        return BlogResponse.from(savedBlog)
    }
    
    fun updateBlog(id: Long, request: BlogUpdateRequest): BlogResponse {
        val existingBlog = blogRepository.findById(id)
            .orElseThrow { IllegalArgumentException("Blog not found with id: $id") }
        
        val updatedBlog = existingBlog.copy(
            title = request.title,
            content = request.content,
            author = request.author,
            updatedAt = LocalDateTime.now()
        )
        
        val savedBlog = blogRepository.save(updatedBlog)
        return BlogResponse.from(savedBlog)
    }
    
    fun deleteBlog(id: Long) {
        if (!blogRepository.existsById(id)) {
            throw IllegalArgumentException("Blog not found with id: $id")
        }
        blogRepository.deleteById(id)
    }
    
    fun getBlogsByAuthor(author: String): List<BlogResponse> {
        return blogRepository.findByAuthor(author).map { BlogResponse.from(it) }
    }
    
    fun searchBlogsByTitle(title: String): List<BlogResponse> {
        return blogRepository.findByTitleContainingIgnoreCase(title).map { BlogResponse.from(it) }
    }
}
