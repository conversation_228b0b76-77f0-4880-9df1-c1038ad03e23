package com.nol.demo.config

import com.nol.demo.entity.Blog
import com.nol.demo.repository.BlogRepository
import org.springframework.boot.CommandLineRunner
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Component
class DataInitializer(
    private val blogRepository: BlogRepository
) : CommandLineRunner {

    override fun run(vararg args: String?) {
        if (blogRepository.count() == 0L) {
            initializeMockData()
        }
    }

    private fun initializeMockData() {
        val mockBlogs = listOf(
            Blog(
                title = "Spring Boot와 Kotlin으로 REST API 개발하기",
                content = """
                    Spring Boot와 Kotlin을 사용하여 REST API를 개발하는 방법에 대해 알아보겠습니다.
                    
                    ## 1. 프로젝트 설정
                    먼저 Spring Boot 프로젝트를 생성하고 필요한 의존성을 추가합니다.
                    
                    ## 2. Entity 설계
                    JPA를 사용하여 데이터베이스 엔티티를 설계합니다.
                    
                    ## 3. Repository 구현
                    Spring Data JPA를 활용하여 데이터 접근 계층을 구현합니다.
                    
                    ## 4. Service 계층
                    비즈니스 로직을 처리하는 서비스 계층을 구현합니다.
                    
                    ## 5. Controller 구현
                    REST API 엔드포인트를 제공하는 컨트롤러를 구현합니다.
                """.trimIndent(),
                author = "김개발",
                createdAt = LocalDateTime.now().minusDays(5),
                updatedAt = LocalDateTime.now().minusDays(5)
            ),
            Blog(
                title = "H2 데이터베이스 활용법",
                content = """
                    개발 환경에서 H2 인메모리 데이터베이스를 효과적으로 활용하는 방법을 소개합니다.
                    
                    ## H2 데이터베이스란?
                    H2는 Java로 작성된 관계형 데이터베이스 관리 시스템입니다.
                    
                    ## 장점
                    - 빠른 성능
                    - 메모리 기반 실행 가능
                    - 웹 콘솔 제공
                    - 테스트 환경에 최적화
                    
                    ## 설정 방법
                    application.properties에서 간단하게 설정할 수 있습니다.
                """.trimIndent(),
                author = "박데이터",
                createdAt = LocalDateTime.now().minusDays(3),
                updatedAt = LocalDateTime.now().minusDays(3)
            ),
            Blog(
                title = "JPA와 Hibernate 기초",
                content = """
                    Java Persistence API(JPA)와 Hibernate ORM에 대한 기초 지식을 정리했습니다.
                    
                    ## JPA란?
                    JPA는 Java 플랫폼에서 관계형 데이터베이스 관리를 위한 API입니다.
                    
                    ## Hibernate
                    Hibernate는 JPA의 구현체 중 하나로, 가장 널리 사용되는 ORM 프레임워크입니다.
                    
                    ## 주요 어노테이션
                    - @Entity: 엔티티 클래스 지정
                    - @Id: 기본 키 지정
                    - @GeneratedValue: 자동 생성 값
                    - @Column: 컬럼 매핑
                    - @Table: 테이블 매핑
                """.trimIndent(),
                author = "이ORM",
                createdAt = LocalDateTime.now().minusDays(2),
                updatedAt = LocalDateTime.now().minusDays(1)
            ),
            Blog(
                title = "Swagger를 이용한 API 문서화",
                content = """
                    SpringDoc OpenAPI를 사용하여 REST API를 자동으로 문서화하는 방법을 알아봅시다.
                    
                    ## Swagger란?
                    Swagger는 REST API를 설계, 빌드, 문서화하기 위한 오픈소스 프레임워크입니다.
                    
                    ## SpringDoc OpenAPI
                    Spring Boot 3.x와 호환되는 OpenAPI 3 문서 생성 라이브러리입니다.
                    
                    ## 설정 방법
                    1. 의존성 추가
                    2. 설정 클래스 작성
                    3. 어노테이션을 통한 API 문서화
                    
                    ## 접속 URL
                    - Swagger UI: /swagger-ui.html
                    - OpenAPI JSON: /v3/api-docs
                """.trimIndent(),
                author = "최문서",
                createdAt = LocalDateTime.now().minusDays(1),
                updatedAt = LocalDateTime.now().minusDays(1)
            ),
            Blog(
                title = "테스트 코드 작성의 중요성",
                content = """
                    소프트웨어 개발에서 테스트 코드 작성이 왜 중요한지, 그리고 어떻게 작성해야 하는지 알아보겠습니다.
                    
                    ## 테스트 코드의 중요성
                    - 코드 품질 향상
                    - 리팩토링 안정성
                    - 버그 조기 발견
                    - 문서화 역할
                    
                    ## 테스트 종류
                    1. 단위 테스트 (Unit Test)
                    2. 통합 테스트 (Integration Test)
                    3. 시스템 테스트 (System Test)
                    
                    ## Spring Boot 테스트
                    - @SpringBootTest
                    - @WebMvcTest
                    - @DataJpaTest
                    - MockMvc 활용
                """.trimIndent(),
                author = "정테스트",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        )

        blogRepository.saveAll(mockBlogs)
        println("Mock 데이터 ${mockBlogs.size}개가 성공적으로 초기화되었습니다.")
    }
}
