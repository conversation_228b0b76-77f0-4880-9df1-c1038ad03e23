package com.nol.demo.dto

import com.nol.demo.entity.Blog
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Schema(description = "블로그 글 생성 요청")
data class BlogCreateRequest(
    @Schema(description = "블로그 글 제목", example = "Spring Boot 시작하기", required = true)
    val title: String,

    @Schema(description = "블로그 글 내용", example = "Spring Boot로 웹 애플리케이션을 개발하는 방법을 알아보겠습니다.", required = true)
    val content: String,

    @Schema(description = "작성자", example = "홍길동", required = true)
    val author: String
)

@Schema(description = "블로그 글 수정 요청")
data class BlogUpdateRequest(
    @Schema(description = "블로그 글 제목", example = "Spring Boot 시작하기 (수정됨)", required = true)
    val title: String,

    @Schema(description = "블로그 글 내용", example = "수정된 내용입니다.", required = true)
    val content: String,

    @Schema(description = "작성자", example = "홍길동", required = true)
    val author: String
)

@Schema(description = "블로그 글 응답")
data class BlogResponse(
    @Schema(description = "블로그 글 ID", example = "1")
    val id: Long,

    @Schema(description = "블로그 글 제목", example = "Spring Boot 시작하기")
    val title: String,

    @Schema(description = "블로그 글 내용", example = "Spring Boot로 웹 애플리케이션을 개발하는 방법을 알아보겠습니다.")
    val content: String,

    @Schema(description = "작성자", example = "홍길동")
    val author: String,

    @Schema(description = "생성일시", example = "2024-01-01T10:00:00")
    val createdAt: LocalDateTime,

    @Schema(description = "수정일시", example = "2024-01-01T10:00:00")
    val updatedAt: LocalDateTime
) {
    companion object {
        fun from(blog: Blog): BlogResponse {
            return BlogResponse(
                id = blog.id!!,
                title = blog.title,
                content = blog.content,
                author = blog.author,
                createdAt = blog.createdAt,
                updatedAt = blog.updatedAt
            )
        }
    }
}
